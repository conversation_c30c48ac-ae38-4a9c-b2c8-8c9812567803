<view class="coupon-center-container">
  <!-- 标签页 -->
  <view class="coupon-tabs">
    <view 
      class="tab-item {{currentTab === item.key ? 'active' : ''}}"
      wx:for="{{tabs}}" 
      wx:key="key"
      bindtap="onTabChange"
      data-tab="{{item.key}}"
    >
      {{item.name}}
    </view>
  </view>
  
  <!-- 内容区域 -->
  <view class="coupon-content">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
    
    <!-- 可使用优惠券 -->
    <view class="coupon-list" wx:if="{{currentTab === 'available' && !loading}}">
      <view
        class="coupon-item {{(item.unavailable_reasons && item.unavailable_reasons.length > 0) || (item.coupon.condition_amount > 0 && orderAmount < item.coupon.condition_amount) ? 'disabled' : 'available'}}"
        wx:for="{{availableCoupons}}"
        wx:key="uniqueId"
      >
        <view class="coupon-left">
          <view class="coupon-amount">
            {{item.displayText}}
          </view>
          <view class="coupon-condition">
            {{item.conditionText}}
          </view>
        </view>
        <view class="coupon-right">
          <view class="coupon-name">{{item.coupon.name}}</view>
          <view class="coupon-desc">{{item.coupon.description}}</view>
          <view class="coupon-time">
            有效期至 {{item.formattedEndTime}}
          </view>
          <view class="coupon-unavailable" wx:if="{{item.unavailable_reasons && item.unavailable_reasons.length > 0}}">
            {{item.unavailable_reasons[0]}}
          </view>
        </view>
        <view class="coupon-action">
          <view class="use-btn" bindtap="useCoupon" data-coupon="{{item}}">
            使用
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{availableCoupons.length === 0}}">
        <image class="empty-icon" src="/images/empty-coupon.png"></image>
        <text class="empty-text">暂无可用优惠券</text>
      </view>
    </view>
    
    <!-- 不可用优惠券 -->
    <view class="coupon-list" wx:if="{{currentTab === 'unavailable' && !loading}}">
      <view
        class="coupon-item disabled"
        wx:for="{{unavailableCoupons}}"
        wx:key="uniqueId"
      >
        <view class="coupon-left">
          <view class="coupon-amount">
            {{item.displayText}}
          </view>
          <view class="coupon-condition">
            {{item.conditionText}}
          </view>
        </view>
        <view class="coupon-right">
          <view class="coupon-name">{{item.coupon.name}}</view>
          <view class="coupon-desc">{{item.coupon.description}}</view>
          <view class="coupon-time">
            有效期至 {{item.formattedEndTime}}
          </view>
          <view class="coupon-unavailable" wx:if="{{item.unavailable_reasons && item.unavailable_reasons.length > 0}}">
            {{item.unavailable_reasons[0]}}
          </view>
        </view>
        <view class="coupon-action">
          <view class="disabled-btn">
            不可用
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{unavailableCoupons.length === 0}}">
        <image class="empty-icon" src="/images/empty-coupon.png"></image>
        <text class="empty-text">暂无不可用优惠券</text>
      </view>
    </view>
    
    <!-- 已使用优惠券 -->
    <view class="coupon-list" wx:if="{{currentTab === 'used' && !loading}}">
      <view
        class="coupon-item used"
        wx:for="{{usedCoupons}}"
        wx:key="uniqueId"
      >
        <view class="coupon-left">
          <view class="coupon-amount">
            {{item.displayText}}
          </view>
          <view class="coupon-condition">
            {{item.conditionText}}
          </view>
        </view>
        <view class="coupon-right">
          <view class="coupon-name">{{item.coupon.name}}</view>
          <view class="coupon-desc">{{item.coupon.description}}</view>
          <view class="coupon-time">
            使用时间：{{item.formattedUsedTime}}
          </view>
        </view>
        <view class="coupon-action">
          <view class="used-btn">
            已使用
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{usedCoupons.length === 0}}">
        <image class="empty-icon" src="/images/empty-coupon.png"></image>
        <text class="empty-text">暂无已使用优惠券</text>
      </view>
    </view>
  </view>
</view>
