Page({
  data: {
    loading: true,
    currentTab: 'available',
    tabs: [
      { key: 'available', name: '可使用' },
      { key: 'unavailable', name: '不可用' },
      { key: 'used', name: '已使用' }
    ],
    availableCoupons: [],
    unavailableCoupons: [],
    usedCoupons: [],
    orderAmount: 0
  },

  onLoad() {
    this.loadCoupons();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadCoupons();
  },

  /**
   * 加载优惠券数据
   */
  loadCoupons() {
    this.setData({ loading: true });

    const app = getApp();
    const userInfo = app.globalData.userInfo;

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      this.setData({ loading: false });
      return;
    }

    // 获取用户优惠券列表
    wx.request({
      url: `${app.globalData.baseUrl}/wechat-mini-app/coupon/user-coupons`,
      method: 'GET',
      header: {
        'token': wx.getStorageSync('token')
      },
      success: (res) => {
        console.log('优惠券数据:', res.data);
        if (res.data.code === 200) {
          this.processCouponData(res.data.data);
        } else {
          wx.showToast({
            title: res.data.message || '获取优惠券失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('获取优惠券失败:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ loading: false });
      }
    });
  },

  /**
   * 处理优惠券数据
   */
  processCouponData(data) {
    const { available_coupons = [], unavailable_coupons = [], used_coupons = [] } = data;

    // 处理可用优惠券
    const availableCoupons = available_coupons.map(item => this.formatCouponItem(item));
    
    // 处理不可用优惠券
    const unavailableCoupons = unavailable_coupons.map(item => this.formatCouponItem(item));
    
    // 处理已使用优惠券
    const usedCoupons = used_coupons.map(item => this.formatCouponItem(item, true));

    this.setData({
      availableCoupons,
      unavailableCoupons,
      usedCoupons
    });
  },

  /**
   * 格式化优惠券项目
   */
  formatCouponItem(item, isUsed = false) {
    const coupon = item.coupon;
    let displayText = '';
    let conditionText = '';

    // 根据优惠券类型设置显示文本
    switch (coupon.type) {
      case 'cash':
        displayText = `¥${coupon.cash_coupon?.amount || 0}`;
        conditionText = coupon.cash_coupon?.condition_amount > 0 
          ? `满¥${coupon.cash_coupon.condition_amount}可用` 
          : '无门槛';
        break;
      case 'discount':
        displayText = `${(coupon.discount_coupon?.discount_rate * 10 || 10)}折`;
        conditionText = coupon.discount_coupon?.condition_amount > 0 
          ? `满¥${coupon.discount_coupon.condition_amount}可用` 
          : '无门槛';
        break;
      case 'full_reduction':
        displayText = `满减¥${coupon.full_reduction_coupon?.reduction_amount || 0}`;
        conditionText = `满¥${coupon.full_reduction_coupon?.condition_amount || 0}可用`;
        break;
      default:
        displayText = '优惠券';
        conditionText = '无门槛';
    }

    // 格式化时间
    let formattedEndTime = '';
    let formattedUsedTime = '';
    
    if (item.coupon_usage_record?.expire_time) {
      const endTime = new Date(item.coupon_usage_record.expire_time);
      formattedEndTime = `${endTime.getFullYear()}-${String(endTime.getMonth() + 1).padStart(2, '0')}-${String(endTime.getDate()).padStart(2, '0')}`;
    } else if (coupon.expire_time) {
      const endTime = new Date(coupon.expire_time);
      formattedEndTime = `${endTime.getFullYear()}-${String(endTime.getMonth() + 1).padStart(2, '0')}-${String(endTime.getDate()).padStart(2, '0')}`;
    }

    if (isUsed && item.coupon_usage_record?.used_time) {
      const usedTime = new Date(item.coupon_usage_record.used_time);
      formattedUsedTime = `${usedTime.getFullYear()}-${String(usedTime.getMonth() + 1).padStart(2, '0')}-${String(usedTime.getDate()).padStart(2, '0')}`;
    }

    return {
      ...item,
      displayText,
      conditionText,
      formattedEndTime,
      formattedUsedTime,
      uniqueId: item.coupon_usage_record?.id || item.coupon?.id || Math.random()
    };
  },

  /**
   * 切换标签页
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  /**
   * 使用优惠券 - 跳转到我要预订页面
   */
  useCoupon(e) {
    const coupon = e.currentTarget.dataset.coupon;
    
    wx.showModal({
      title: '使用优惠券',
      content: '是否前往预订页面使用此优惠券？',
      success: (res) => {
        if (res.confirm) {
          // 跳转到我要预订页面
          wx.navigateTo({
            url: '/pages/booking/booking'
          });
        }
      }
    });
  }
});
