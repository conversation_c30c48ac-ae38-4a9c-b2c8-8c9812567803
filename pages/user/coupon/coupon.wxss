/* 页面容器 */
.coupon-center-container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 标签页 */
.coupon-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #4080ff;
  font-weight: 600;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #4080ff;
  border-radius: 2rpx;
}

/* 内容区域 */
.coupon-content {
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4080ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 优惠券列表 */
.coupon-list {
  padding-bottom: 40rpx;
}

/* 优惠券项目 */
.coupon-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.coupon-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background: #4080ff;
}

.coupon-item.disabled::before {
  background: #ccc;
}

.coupon-item.used::before {
  background: #999;
}

.coupon-item.disabled,
.coupon-item.used {
  opacity: 0.6;
}

/* 优惠券左侧 */
.coupon-left {
  margin-right: 30rpx;
  text-align: center;
  min-width: 160rpx;
}

.coupon-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #4080ff;
  margin-bottom: 8rpx;
}

.coupon-item.disabled .coupon-amount,
.coupon-item.used .coupon-amount {
  color: #999;
}

.coupon-condition {
  font-size: 22rpx;
  color: #999;
}

/* 优惠券右侧 */
.coupon-right {
  flex: 1;
  margin-right: 20rpx;
}

.coupon-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.coupon-time {
  font-size: 22rpx;
  color: #999;
}

.coupon-unavailable {
  font-size: 22rpx;
  color: #ff4444;
  margin-top: 8rpx;
}

/* 优惠券操作按钮 */
.coupon-action {
  display: flex;
  align-items: center;
}

.use-btn {
  background: #4080ff;
  color: #fff;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  font-weight: 600;
  border: none;
}

.use-btn:active {
  background: #3366cc;
}

.disabled-btn,
.used-btn {
  background: #f5f5f5;
  color: #999;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border: 1rpx solid #ddd;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
